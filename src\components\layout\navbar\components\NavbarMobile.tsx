import Link from "next/link";
import { navigationNavItems } from "../../data/navigationNavItems";

export default function NavbarMobile() {
  return (
    <div className="md:hidden">
      <nav className="md:hidden fixed bottom-0 left-0 right-0 bg-primary/90 backdrop-blur-sm border-t border-white/10">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center space-x-4">
            {navigationNavItems.map((item) => (
              <Link key={item.href} href={item.href} className="text-secondary hover:text-white transition-colors">
                {item.icon}
              </Link>
            ))}
          </div>
        </div>
      </nav>
    </div>
  );
}