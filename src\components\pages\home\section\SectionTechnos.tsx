import { technoData } from "@/components/layout/data/technoData";

export default function SectionTechnos() {
  // Regrouper les technologies par catégorie
  const categories = {
    langages: technoData.filter(tech => ['HTML', 'CSS', 'JavaScript', 'TypeScript'].includes(tech.name)),
    frameworks: technoData.filter(tech => ['React', 'Next.js', 'Express'].includes(tech.name)),
    basesDeDonnees: technoData.filter(tech => ['MongoDB', 'MySQL'].includes(tech.name)),
    outils: technoData.filter(tech => ['Git', 'Node.js'].includes(tech.name)),
  };

  return (
    <section className="container mx-auto px-4 pt-28 pb-16 md:pt-32 md:pb-24">
      <h2 className="text-3xl font-heading font-bold tracking-wider text-center mb-12">
        Technologies que j'utilise
      </h2>
      {Object.entries(categories).map(([category, techs]) => (
        <div key={category} className="mb-12">
          <h3 className="text-2xl font-heading font-semibold tracking-wider text-center mb-8 capitalize">
            {category.replace(/([A-Z])/g, ' $1').trim()}
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8">
            {techs.map((techno) => (
              <div key={techno.name} className="flex flex-col justify-center items-center w-40 px-6 py-3 bg-white/5 rounded-full text-white/90 border border-white/10">
                <techno.icon className={`text-4xl mb-4 ${techno.color}`} />
                <span className="text-lg font-body font-medium">{techno.name}</span>
              </div>
            ))}
          </div>
        </div>
      ))}
    </section>
  );
}