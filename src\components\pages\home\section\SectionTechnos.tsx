import { technoData } from "@/components/layout/data/technoData";
import { BsArrowRight } from "react-icons/bs";

export default function SectionTechnos() {
  return (
    <section className="container mx-auto px-4 py-20 md:py-28">
      {/* En-tête de section */}
      <header className="text-center mb-16">
        <h2 className="text-4xl md:text-5xl font-heading font-bold tracking-wider text-text-primary mb-4">
          Technologies
        </h2>
        <p className="text-xl text-text-secondary font-display max-w-2xl mx-auto">
          Les outils et frameworks que j'utilise pour créer des expériences web exceptionnelles
        </p>
        <div className="w-24 h-1 bg-gradient-to-r from-secondary to-accent mx-auto mt-6 rounded-full"></div>
      </header>

      {/* Grille des technologies */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-6 md:gap-8">
        {technoData.map((techno, index) => (
          <article
            key={techno.name}
            className="group relative"
            style={{ animationDelay: `${index * 100}ms` }}
          >
            {/* Effet de fond animé */}
            <div className="absolute -inset-0.5 bg-gradient-to-r from-secondary to-accent rounded-2xl opacity-0 group-hover:opacity-100 transition-all duration-300 blur-sm"></div>

            {/* Carte principale */}
            <div className="relative flex flex-col items-center justify-center p-6 bg-primary-light/50 backdrop-blur-sm rounded-2xl border border-white/10 transition-all duration-300 hover:border-accent/50 hover:transform hover:scale-105 hover:shadow-xl hover:shadow-accent/20">
              {/* Icône */}
              <div className="mb-4 p-3 rounded-xl bg-white/5 group-hover:bg-white/10 transition-all duration-300">
                <techno.icon className={`text-3xl md:text-4xl ${techno.color} group-hover:scale-110 transition-transform duration-300`} />
              </div>

              {/* Nom de la technologie */}
              <h3 className="text-sm md:text-base font-body font-medium text-text-secondary group-hover:text-text-primary transition-colors duration-300 text-center">
                {techno.name}
              </h3>

              {/* Indicateur visuel */}
              <div className="mt-2 w-8 h-0.5 bg-gradient-to-r from-accent to-secondary rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300"></div>
            </div>
          </article>
        ))}
      </div>

      {/* Section CTA optionnelle */}
      <footer className="text-center mt-16">
        <p className="text-text-muted font-body mb-6">
          Curieux de voir ces technologies en action ?
        </p>
        <a
          href="/projects"
          className="inline-flex items-center px-8 py-3 bg-gradient-to-r from-secondary to-accent text-white font-medium rounded-xl transition-all duration-300 hover:shadow-lg hover:shadow-accent/25 hover:scale-105"
        >
          Découvrir mes projets
          <BsArrowRight className="ml-2 text-lg" />
        </a>
      </footer>
    </section>
  );
}

