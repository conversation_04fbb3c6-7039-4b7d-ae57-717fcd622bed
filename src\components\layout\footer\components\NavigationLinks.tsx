import Link from "next/link";

interface NavigationItem {
  href: string;
  label: string;
}

const navigationItems: NavigationItem[] = [
  { href: "/", label: "Accueil" },
  { href: "/projects", label: "Projets" },
  { href: "/skills", label: "Compétences" },
  { href: "/about", label: "À propos" },
  { href: "/contact", label: "Contact" },
];

export default function NavigationLinks() {
  return (
    <div>
      <h3 className="text-white font-heading text-lg font-semibold mb-4">
        Navigation
      </h3>
      <ul className="space-y-2 font-body">
        {navigationItems.map((item) => (
          <li key={item.href}>
            <Link 
              href={item.href} 
              className="text-white/70 hover:text-secondary transition-colors"
            >
              {item.label}
            </Link>
          </li>
        ))}
      </ul>
    </div>
  );
}
