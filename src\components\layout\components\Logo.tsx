import Link from "next/link";
import { BsCode } from "react-icons/bs";

interface LogoProps {
  href: string;
  label?: string;
  variant?: "full" | "initials" | "icon";
  size?: "sm" | "md" | "lg";
}

export default function Logo({
  href,
  variant = "initials",
  size = "lg"
}: LogoProps) {

  // Tailles selon la prop size
  const sizeClasses = {
    sm: "text-lg",
    md: "text-xl",
    lg: "text-2xl"
  };

  // Logo avec initiales et symbole de code fusionnés
  const InitialsLogo = () => (
    <div className="flex items-center">
      {/* Conteneur du logo avec effet */}
      <div className="relative group">

        {/* Logo principal */}
        <div className="relative flex items-center justify-center w-10 h-10">
          {/* Combinaison SP avec icône code */}
          <div className="flex items-center justify-center space-x-0.5">
            {/* Icône code à gauche */}
            <BsCode className="text-xs text-secondary group-hover:text-accent transition-colors duration-300" />

            {/* Initiales SP */}
            <span className="text-sm font-heading font-bold bg-gradient-to-r from-secondary to-accent bg-clip-text text-transparent group-hover:scale-110 transition-transform duration-300">
              SP
            </span>
          </div>
        </div>
      </div>
    </div>
  );

  // Logo avec icône personnalisée
  const IconLogo = () => (
    <div className="flex items-center space-x-3">
      {/* Icône SVG personnalisée */}
      <div className="relative group">
        <div className="absolute -inset-1 bg-gradient-to-r from-secondary to-accent rounded-full opacity-70 group-hover:opacity-100 transition-opacity duration-300 blur-sm"></div>
        <div className="relative w-8 h-8 bg-primary-light rounded-full border border-white/20 group-hover:border-accent/50 transition-all duration-300 flex items-center justify-center">
          <svg
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            className="text-secondary group-hover:text-accent transition-colors duration-300"
          >
            {/* Code symbol stylisé */}
            <path
              d="M8 6L2 12L8 18M16 6L22 12L16 18M12 4L8 20"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
      </div>

      
    </div>
  );

  // Logo texte simple mais stylisé
  const FullLogo = () => (
    <div className="group">
      <span className={`font-heading font-bold bg-gradient-to-r from-secondary via-accent to-secondary bg-clip-text text-transparent hover:from-accent hover:via-secondary hover:to-accent transition-all duration-500 ${sizeClasses[size]}`}>
        SP
      </span>
      <div className="w-0 group-hover:w-full h-0.5 bg-gradient-to-r from-secondary to-accent transition-all duration-300 mt-1"></div>
    </div>
  );

  // Sélection du variant
  const renderLogo = () => {
    switch (variant) {
      case "initials":
        return <InitialsLogo />;
      case "icon":
        return <IconLogo />;
      default:
        return <FullLogo />;
    }
  };

  return (
    <Link href={href} className="block">
      {renderLogo()}
    </Link>
  );
}