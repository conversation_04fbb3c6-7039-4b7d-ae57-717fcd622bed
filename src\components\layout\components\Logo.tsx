import Link from "next/link";

interface LogoProps {
  href: string;
  label: string;
  variant?: "full" | "initials" | "icon";
  size?: "sm" | "md" | "lg";
}

export default function Logo({
  href,
  label,
  variant = "full",
  size = "md"
}: LogoProps) {

  // Tailles selon la prop size
  const sizeClasses = {
    sm: "text-lg",
    md: "text-xl",
    lg: "text-2xl"
  };

  // Logo avec initiales et symbole de code fusionnés
  const InitialsLogo = () => (
    <div className="flex items-center space-x-3">
      {/* Conteneur du logo avec effet */}
      <div className="relative group">
        {/* Effet de fond animé */}
        <div className="absolute -inset-1 bg-gradient-to-r from-secondary to-accent rounded-xl opacity-70 group-hover:opacity-100 transition-opacity duration-300 blur-sm"></div>

        {/* Logo principal */}
        <div className="relative flex items-center justify-center w-12 h-12 bg-primary-light rounded-xl border border-white/20 group-hover:border-accent/50 transition-all duration-300">
          {/* SVG personnalisé combinant SP et </> */}
          <svg
            width="28"
            height="28"
            viewBox="0 0 40 40"
            className="group-hover:scale-110 transition-transform duration-300"
          >
            {/* Symbole < à gauche */}
            <path
              d="M8 12L4 20L8 28"
              stroke="url(#gradient1)"
              strokeWidth="2.5"
              strokeLinecap="round"
              strokeLinejoin="round"
              fill="none"
            />

            {/* Lettre S stylisée au centre */}
            <path
              d="M16 14C16 12 17 11 19 11C21 11 22 12 22 14C22 16 20 17 18 18C16 19 14 20 14 22C14 24 15 25 17 25C19 25 20 24 20 22"
              stroke="url(#gradient2)"
              strokeWidth="2.5"
              strokeLinecap="round"
              fill="none"
            />

            {/* Lettre P stylisée */}
            <path
              d="M24 11V25M24 11H28C30 11 31 12 31 14C31 16 30 17 28 17H24"
              stroke="url(#gradient3)"
              strokeWidth="2.5"
              strokeLinecap="round"
              strokeLinejoin="round"
              fill="none"
            />

            {/* Symbole > à droite */}
            <path
              d="M32 12L36 20L32 28"
              stroke="url(#gradient4)"
              strokeWidth="2.5"
              strokeLinecap="round"
              strokeLinejoin="round"
              fill="none"
            />

            {/* Définition des dégradés */}
            <defs>
              <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#E67E22" />
                <stop offset="100%" stopColor="#2ECCC6" />
              </linearGradient>
              <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#2ECCC6" />
                <stop offset="100%" stopColor="#E67E22" />
              </linearGradient>
              <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#E67E22" />
                <stop offset="100%" stopColor="#2ECCC6" />
              </linearGradient>
              <linearGradient id="gradient4" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#2ECCC6" />
                <stop offset="100%" stopColor="#E67E22" />
              </linearGradient>
            </defs>
          </svg>
        </div>
      </div>

      {/* Nom complet */}
      <div className="hidden sm:block">
        <span className={`font-heading font-bold text-text-primary hover:text-secondary transition-colors duration-300 ${sizeClasses[size]}`}>
          {label}
        </span>
      </div>
    </div>
  );

  // Logo avec icône personnalisée
  const IconLogo = () => (
    <div className="flex items-center space-x-3">
      {/* Icône SVG personnalisée */}
      <div className="relative group">
        <div className="absolute -inset-1 bg-gradient-to-r from-secondary to-accent rounded-full opacity-70 group-hover:opacity-100 transition-opacity duration-300 blur-sm"></div>
        <div className="relative w-8 h-8 bg-primary-light rounded-full border border-white/20 group-hover:border-accent/50 transition-all duration-300 flex items-center justify-center">
          <svg
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            className="text-secondary group-hover:text-accent transition-colors duration-300"
          >
            {/* Code symbol stylisé */}
            <path
              d="M8 6L2 12L8 18M16 6L22 12L16 18M12 4L8 20"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
      </div>

      <span className={`font-heading font-bold text-text-primary hover:text-secondary transition-colors duration-300 ${sizeClasses[size]}`}>
        {label}
      </span>
    </div>
  );

  // Logo texte simple mais stylisé
  const FullLogo = () => (
    <div className="group">
      <span className={`font-heading font-bold bg-gradient-to-r from-secondary via-accent to-secondary bg-clip-text text-transparent hover:from-accent hover:via-secondary hover:to-accent transition-all duration-500 ${sizeClasses[size]}`}>
        {label}
      </span>
      <div className="w-0 group-hover:w-full h-0.5 bg-gradient-to-r from-secondary to-accent transition-all duration-300 mt-1"></div>
    </div>
  );

  // Sélection du variant
  const renderLogo = () => {
    switch (variant) {
      case "initials":
        return <InitialsLogo />;
      case "icon":
        return <IconLogo />;
      default:
        return <FullLogo />;
    }
  };

  return (
    <Link href={href} className="block">
      {renderLogo()}
    </Link>
  );
}