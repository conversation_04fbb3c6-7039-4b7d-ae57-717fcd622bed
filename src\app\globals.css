@import "tailwindcss";

@theme {
  /* colors blue */
  --color-primary: #000f1f; /* blue royal */
  /* colors orange */
  --color-secondary: #E67E22; /* orange discret */

  /* colors teal - troisième couleur */
  --color-accent: #2ECCC6; /* turquoise/teal */

  /* colors white */
  --color-white: #FFFFFF; /* blanc */


  /* fonts */
  --font-heading: 'Roboto', sans-serif;
  --font-display: 'Poppins', sans-serif;
  --font-body:'Open Sans', sans-serif;
  --font-mono:'Fira Code', monospace;
}


body {
  @apply w-full h-screen bg-primary;
}

.animate-gradient {
  background-size: 400% 400%;
  animation: gradient 5s ease infinite;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

@keyframes gradient {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}