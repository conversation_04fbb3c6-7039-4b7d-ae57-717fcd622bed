import TechnologyCategory from "./TechnologyCategory";

interface TechnologyCategoriesGridProps {
  categories: Array<{
    id: string;
    title: string;
    description: string;
    color: string;
    technologies: Array<{
      name: string;
      color: string;
      icon: any;
      level: number;
      description: string;
    }>;
  }>;
}

export default function TechnologyCategoriesGrid({
  categories
}: TechnologyCategoriesGridProps) {
  return (
    <div className="space-y-16">
      {categories.map((category, index) => (
        <TechnologyCategory
          key={category.id}
          category={category}
          animationDelay={index * 200}
        />
      ))}
    </div>
  );
}
